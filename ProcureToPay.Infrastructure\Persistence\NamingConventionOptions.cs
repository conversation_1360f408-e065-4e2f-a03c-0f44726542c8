namespace ProcureToPay.Infrastructure.Persistence
{
    /// <summary>
    /// Configuration options for controlling snake_case naming convention behavior.
    /// Allows fine-grained control over what gets converted and what doesn't.
    /// </summary>
    public class NamingConventionOptions
    {
        /// <summary>
        /// Gets or sets whether snake_case conversion is enabled globally.
        /// When false, no naming convention changes are applied.
        /// </summary>
        public bool EnableSnakeCaseConversion { get; set; } = true;

        /// <summary>
        /// Gets or sets whether table names should be converted to snake_case.
        /// </summary>
        public bool ConvertTableNames { get; set; } = true;

        /// <summary>
        /// Gets or sets whether column names should be converted to snake_case.
        /// </summary>
        public bool ConvertColumnNames { get; set; } = true;

        /// <summary>
        /// Gets or sets whether index names should be converted to snake_case.
        /// </summary>
        public bool ConvertIndexNames { get; set; } = true;

        /// <summary>
        /// Gets or sets whether constraint names should be converted to snake_case.
        /// </summary>
        public bool ConvertConstraintNames { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to enable detailed logging of naming convention changes.
        /// </summary>
        public bool EnableLogging { get; set; } = false;

        /// <summary>
        /// Gets or sets whether to perform validation checks before applying naming conventions.
        /// This helps prevent conflicts with existing explicit column names.
        /// </summary>
        public bool EnableValidation { get; set; } = true;
    }
}
