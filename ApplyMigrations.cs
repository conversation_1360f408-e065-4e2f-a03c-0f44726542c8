using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using ProcureToPay.Domain.Interfaces;
using ProcureToPay.Infrastructure.Persistence;
using ProcureToPay.WebApp.Services;
using System;
using System.IO;

namespace ProcureToPay.Migrations
{
    public class Program
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("Starting migration application...");

            // Build configuration
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            // Get connection string
            var connectionString = configuration.GetConnectionString("postgresdb");
            Console.WriteLine($"Connection string: {connectionString}");

            // Create services
            var services = new ServiceCollection();

            // Add DbContext
            services.AddDbContext<ApplicationDbContext>(options =>
            {
                options.UseNpgsql(connectionString, sqlOptions =>
                {
                    // Add retry on failure
                    sqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 5,
                        maxRetryDelay: TimeSpan.FromSeconds(30),
                        errorCodesToAdd: null);
                });

                // Note: Snake case naming convention is now handled in ApplicationDbContext.OnModelCreating()
                // to avoid compatibility issues with EFCore.NamingConventions library in .NET 9 preview
            });

            // Add tenant provider
            services.AddScoped<ITenantProvider, DefaultTenantProvider>();

            // Build service provider
            var serviceProvider = services.BuildServiceProvider();

            try
            {
                // Get DbContext
                using (var scope = serviceProvider.CreateScope())
                {
                    var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

                    Console.WriteLine("Checking database connection...");
                    if (dbContext.Database.CanConnect())
                    {
                        Console.WriteLine("Database connection successful.");

                        Console.WriteLine("Applying migrations...");
                        dbContext.Database.Migrate();
                        Console.WriteLine("Migrations applied successfully.");
                    }
                    else
                    {
                        Console.WriteLine("Cannot connect to database.");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner error: {ex.InnerException.Message}");
                }
            }

            Console.WriteLine("Migration application completed.");
        }
    }
}
